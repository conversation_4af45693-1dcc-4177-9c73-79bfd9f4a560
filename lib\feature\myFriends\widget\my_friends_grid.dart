import 'package:flutter/material.dart';
import 'package:movie_proj/core/my_images.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/auth/widget/my_text_btns.dart';
import 'package:movie_proj/feature/chat/chat_screen.dart';
import 'package:movie_proj/feature/auth/model/user_model.dart';
import 'package:movie_proj/feature/myFriends/model/friendship_model.dart';
import 'package:movie_proj/feature/myFriends/service/friends_service.dart';

enum FriendsGridMode {
  addFriends,
  myFriends,
}

class MyFriendsGrid extends StatefulWidget {
  final FriendsGridMode mode;

  const MyFriendsGrid({
    super.key,
    required this.mode,
  });

  @override
  State<MyFriendsGrid> createState() => _MyFriendsGridState();
}

class _MyFriendsGridState extends State<MyFriendsGrid> {
  List<UserModel> _availableUsers = [];
  List<FriendshipModel> _friends = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      if (widget.mode == FriendsGridMode.addFriends) {
        final users = await FriendsService.getAvailableUsers();
        if (mounted) {
          setState(() {
            _availableUsers = users;
            _isLoading = false;
          });
        }
      } else {
        final friends = await FriendsService.getFriends();
        if (mounted) {
          setState(() {
            _friends = friends;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToChat(
      BuildContext context, String friendName, String friendImage) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          friendName: friendName,
          friendImage:
              friendImage.isNotEmpty ? friendImage : MyImages.profilePic,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required double buttonHeight,
    required FriendsGridMode mode,
    required BuildContext context,
    UserModel? user,
    FriendshipModel? friendship,
  }) {
    switch (mode) {
      case FriendsGridMode.addFriends:
        return MyTextBtn(
          onTap: user != null ? () => _sendFriendRequest(user) : () {},
          text: MyText.add,
          color: user != null ? Colors.blue : Colors.grey,
          textColor: Colors.white,
          radius: buttonHeight / 2,
        );
      case FriendsGridMode.myFriends:
        if (friendship != null) {
          final currentUserId = FriendsService.currentUserId;
          if (currentUserId != null) {
            final friendInfo = friendship.getFriendInfo(currentUserId);
            return MyTextBtn(
              onTap: () => _navigateToChat(
                context,
                friendInfo['name'] ?? 'Friend',
                friendInfo['image'] ?? '',
              ),
              text: MyText.chat,
              color: Colors.green,
              textColor: Colors.white,
              radius: buttonHeight / 2,
            );
          }
        }
        return MyTextBtn(
          onTap: () {},
          text: MyText.chat,
          color: Colors.grey,
          textColor: Colors.white,
          radius: buttonHeight / 2,
        );
    }
  }

  Future<void> _sendFriendRequest(UserModel user) async {
    try {
      final success = await FriendsService.sendFriendRequest(user);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Friend request sent to ${user.name}!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
          // Refresh the list to remove the user from available users
          _loadData();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send friend request'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            vSpace(16),
            Text(
              'Error loading data',
              style: MyStyles.title24White400.copyWith(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
            vSpace(8),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final items =
        widget.mode == FriendsGridMode.addFriends ? _availableUsers : _friends;

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.mode == FriendsGridMode.addFriends
                  ? Icons.person_search
                  : Icons.people_outline,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            vSpace(16),
            Text(
              widget.mode == FriendsGridMode.addFriends
                  ? MyText.noUsersAvailable
                  : MyText.noFriendsYet,
              style: MyStyles.title24White400.copyWith(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = _getCrossAxisCount(constraints.maxWidth);
        final spacing = _getSpacing(constraints.maxWidth);
        final imageSize = _getImageSize(constraints.maxWidth);
        final fontSize = _getFontSize(constraints.maxWidth);
        final buttonHeight = _getButtonHeight(constraints.maxWidth);

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisExtent: _getCardHeight(constraints.maxWidth),
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
          ),
          itemBuilder: (context, index) {
            if (widget.mode == FriendsGridMode.addFriends) {
              final user = _availableUsers[index];
              return _buildUserCard(
                user: user,
                constraints: constraints,
                spacing: spacing,
                imageSize: imageSize,
                fontSize: fontSize,
                buttonHeight: buttonHeight,
              );
            } else {
              final friendship = _friends[index];
              final currentUserId = FriendsService.currentUserId;
              if (currentUserId != null) {
                final friendInfo = friendship.getFriendInfo(currentUserId);
                return _buildFriendCard(
                  friendship: friendship,
                  friendInfo: friendInfo,
                  constraints: constraints,
                  spacing: spacing,
                  imageSize: imageSize,
                  fontSize: fontSize,
                  buttonHeight: buttonHeight,
                );
              }
            }
            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildUserCard({
    required UserModel user,
    required BoxConstraints constraints,
    required double spacing,
    required double imageSize,
    required double fontSize,
    required double buttonHeight,
  }) {
    return Container(
      padding: EdgeInsets.all(spacing / 2),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.2),
        borderRadius:
            BorderRadius.circular(_getBorderRadius(constraints.maxWidth)),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(imageSize / 2),
            child: Image.asset(
              user.image?.isNotEmpty == true
                  ? user.image!
                  : MyImages.profilePic,
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(imageSize / 2),
                  ),
                  child: Icon(
                    Icons.person,
                    color: Colors.grey,
                    size: imageSize / 2,
                  ),
                );
              },
            ),
          ),
          hSpace(spacing),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name ?? 'Unknown User',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: fontSize,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                vSpace(spacing / 2),
                Text(
                  user.email ?? '',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: fontSize * 0.8,
                    color: Colors.white70,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                vSpace(spacing / 2),
                SizedBox(
                  height: buttonHeight,
                  child: _buildActionButton(
                    buttonHeight: buttonHeight,
                    mode: widget.mode,
                    context: context,
                    user: user,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendCard({
    required FriendshipModel friendship,
    required Map<String, String> friendInfo,
    required BoxConstraints constraints,
    required double spacing,
    required double imageSize,
    required double fontSize,
    required double buttonHeight,
  }) {
    return InkWell(
      onTap: () => _navigateToChat(
        context,
        friendInfo['name'] ?? 'Friend',
        friendInfo['image'] ?? '',
      ),
      child: Container(
        padding: EdgeInsets.all(spacing / 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.2),
          borderRadius:
              BorderRadius.circular(_getBorderRadius(constraints.maxWidth)),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(imageSize / 2),
              child: Image.asset(
                friendInfo['image']?.isNotEmpty == true
                    ? friendInfo['image']!
                    : MyImages.profilePic,
                width: imageSize,
                height: imageSize,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: imageSize,
                    height: imageSize,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(imageSize / 2),
                    ),
                    child: Icon(
                      Icons.person,
                      color: Colors.grey,
                      size: imageSize / 2,
                    ),
                  );
                },
              ),
            ),
            hSpace(spacing),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    friendInfo['name'] ?? 'Friend',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: fontSize,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  vSpace(spacing / 2),
                  Text(
                    '${MyText.friendsSince} ${_formatDate(friendship.createdAt)}',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: fontSize * 0.8,
                      color: Colors.white70,
                    ),
                  ),
                  vSpace(spacing / 2),
                  SizedBox(
                    height: buttonHeight,
                    child: _buildActionButton(
                      buttonHeight: buttonHeight,
                      mode: widget.mode,
                      context: context,
                      friendship: friendship,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} year${(difference.inDays / 365).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else {
      return 'Today';
    }
  }

  int _getCrossAxisCount(double width) {
    if (width <= 400) return 1;
    if (width <= 700) return 2;
    return 3;
  }

  double _getSpacing(double width) {
    if (width <= 400) return 8;
    if (width <= 700) return 12;
    return 16;
  }

  double _getImageSize(double width) {
    if (width <= 400) return 48;
    if (width <= 700) return 56;
    return 64;
  }

  double _getFontSize(double width) {
    if (width <= 400) return 14;
    if (width <= 700) return 16;
    return 18;
  }

  double _getButtonHeight(double width) {
    if (width <= 400) return 28;
    if (width <= 700) return 32;
    return 36;
  }

  double _getBorderRadius(double width) {
    if (width <= 400) return 8;
    if (width <= 700) return 12;
    return 16;
  }

  double _getCardHeight(double width) {
    if (width <= 400) return 120;
    if (width <= 700) return 160;
    return 200;
  }
}
