import 'package:flutter/material.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_images.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/search/search_screen.dart';
import 'package:movie_proj/feature/myFriends/model/friend_request_model.dart';
import 'package:movie_proj/feature/myFriends/service/friends_service.dart';
import 'package:movie_proj/feature/myFriends/widget/friend_request_dialog.dart';

class MovieAppBar extends StatefulWidget implements PreferredSizeWidget {
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  final int currentIndex;
  final Function(int) onNavigate;

  const MovieAppBar({
    super.key,
    required this.currentIndex,
    required this.onNavigate,
  });

  @override
  State<MovieAppBar> createState() => _MovieAppBarState();
}

class _MovieAppBarState extends State<MovieAppBar> {
  List<FriendRequestModel> _friendRequests = [];
  bool _isLoadingRequests = false;

  @override
  void initState() {
    super.initState();
    _loadFriendRequests();
  }

  Future<void> _loadFriendRequests() async {
    if (_isLoadingRequests) return;

    setState(() {
      _isLoadingRequests = true;
    });

    try {
      print('🔍 Loading friend requests...');
      final currentUserId = FriendsService.currentUserId;
      print('📱 Current User ID: $currentUserId');

      final requests = await FriendsService.getPendingFriendRequests();
      print('📬 Found ${requests.length} pending friend requests');

      if (mounted) {
        setState(() {
          _friendRequests = requests;
          _isLoadingRequests = false;
        });
        print('✅ Friend requests loaded successfully');
      }
    } catch (e) {
      print('❌ Error loading friend requests: $e');
      if (mounted) {
        setState(() {
          _isLoadingRequests = false;
        });
      }
    }
  }

  void _showFriendRequestsDialog() {
    showDialog(
      context: context,
      builder: (context) => FriendRequestDialog(
        friendRequests: _friendRequests,
        onRequestHandled: () {
          _loadFriendRequests(); // Refresh the list
          Navigator.of(context).pop(); // Close dialog
        },
      ),
    );
  }

  // Debug function to create a test friend request
  Future<void> _createTestFriendRequest() async {
    final success = await FriendsService.createTestFriendRequest();
    if (success) {
      _loadFriendRequests(); // Refresh
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🎉 Test friend request created!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Failed to create test friend request'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: MyColors.primaryColor,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  MyText.mood,
                  style: MyStyles.title13Redw700,
                ),
                Text(
                  MyText.box,
                  style: MyStyles.title24White700.copyWith(fontSize: 20),
                ),
              ],
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.home,
              index: 0,
              isActive: widget.currentIndex == 0,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.suggest,
              index: 1,
              isActive: widget.currentIndex == 1,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.myList,
              index: 2,
              isActive: widget.currentIndex == 2,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.friends,
              index: 3,
              isActive: widget.currentIndex == 3,
            ),
            const Spacer(),
            // Notification Icon for Friend Requests
            _buildNotificationIcon(),
            hSpace(15),
            IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SearchScreen(),
                  ),
                );
              },
              icon: const Icon(
                Icons.search,
                color: Colors.white,
              ),
            ),
            hSpace(15),
            GestureDetector(
              onTap: () => widget.onNavigate(4), // Profile screen index
              child: const CircleAvatar(
                backgroundImage: AssetImage(MyImages.profilePic),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    final hasNotifications = _friendRequests.isNotEmpty;

    return Stack(
      children: [
        GestureDetector(
          onTap: _showFriendRequestsDialog,
          onLongPress:
              _createTestFriendRequest, // Long press to create test data
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              Icons.notifications_outlined,
              color: hasNotifications ? Colors.orange : Colors.white,
              size: 24,
            ),
          ),
        ),
        if (hasNotifications)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _friendRequests.length.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNavigationItem({
    required String text,
    required int index,
    required bool isActive,
  }) {
    return GestureDetector(
      onTap: () => widget.onNavigate(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: isActive
            ? BoxDecoration(
                color: MyColors.btnColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        child: Text(
          text,
          style: isActive
              ? MyStyles.title24White700.copyWith(fontSize: 13)
              : MyStyles.title24White400.copyWith(fontSize: 13),
        ),
      ),
    );
  }
}
