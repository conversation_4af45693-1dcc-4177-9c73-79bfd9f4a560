import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:movie_proj/feature/auth/model/user_model.dart';
import 'package:movie_proj/feature/myFriends/model/friend_request_model.dart';
import 'package:movie_proj/feature/myFriends/model/friendship_model.dart';

class FriendsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  static String? get currentUserId => _auth.currentUser?.uid;

  // Collections
  static CollectionReference get _usersCollection =>
      _firestore.collection('users');
  static CollectionReference get _friendRequestsCollection =>
      _firestore.collection('friendRequests');
  static CollectionReference get _friendshipsCollection =>
      _firestore.collection('friendships');

  // Get all users except current user and existing friends
  static Future<List<UserModel>> getAvailableUsers() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return [];

      // Get existing friends first
      final friendships = await getFriends();
      final friendIds = friendships.map((friendship) {
        if (friendship.user1Id == currentUser) {
          return friendship.user2Id;
        } else {
          return friendship.user1Id;
        }
      }).toSet(); // Using Set for faster lookups

      // Get ALL pending friend requests (both sent and received)
      final receivedRequests = await getPendingFriendRequests();
      final sentRequestsSnapshot = await _friendRequestsCollection
          .where('senderId', isEqualTo: currentUser)
          .where('status', isEqualTo: 'pending')
          .get();

      final sentRequests = sentRequestsSnapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();

      // Combine both sent and received requests to get all pending user IDs
      final pendingIds = <String>{};

      // Add users we sent requests to
      for (var request in sentRequests) {
        pendingIds.add(request.receiverId);
      }

      // Add users who sent us requests
      for (var request in receivedRequests) {
        pendingIds.add(request.senderId);
      }

      // Get all users except current user, friends, and pending requests
      final QuerySnapshot<Map<String, dynamic>> usersSnapshot =
          await _usersCollection.where('uId', isNotEqualTo: currentUser).get()
              as QuerySnapshot<Map<String, dynamic>>;

      final availableUsers = usersSnapshot.docs
          .map((doc) {
            final data = doc.data();
            return UserModel.fromJson(data);
          })
          .where((user) =>
              user.uId != null &&
              user.uId!.isNotEmpty &&
              !friendIds.contains(user.uId) &&
              !pendingIds.contains(user.uId))
          .toList();

      if (kDebugMode) {
        print('\n🔍 === AVAILABLE USERS ANALYSIS ===');
        print('👥 Total users found: ${usersSnapshot.docs.length}');
        print('🤝 Current friends (${friendIds.length}): $friendIds');
        print('⏳ Pending requests (${pendingIds.length}): $pendingIds');
        print('✅ Available users (${availableUsers.length}):');
        for (var user in availableUsers) {
          print('   - ${user.name} (${user.uId})');
        }
        print('=================================\n');
      }

      return availableUsers;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available users: $e');
      }
      return [];
    }
  }

  // Get user's friends
  static Future<List<FriendshipModel>> getFriends() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return [];

      final friendshipsSnapshot = await _friendshipsCollection
          .where('user1Id', isEqualTo: currentUser)
          .get();

      final friendshipsSnapshot2 = await _friendshipsCollection
          .where('user2Id', isEqualTo: currentUser)
          .get();

      final allFriendships = [
        ...friendshipsSnapshot.docs,
        ...friendshipsSnapshot2.docs,
      ];

      return allFriendships
          .map((doc) => FriendshipModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting friends: $e');
      }
      return [];
    }
  }

  // Send friend request with real-time updates
  static Future<bool> sendFriendRequest(UserModel targetUser) async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return false;

      // Get current user data
      final currentUserDoc = await _usersCollection.doc(currentUser).get();
      if (!currentUserDoc.exists) return false;

      final currentUserData =
          UserModel.fromJson(currentUserDoc.data() as Map<String, dynamic>);

      // Check if request already exists
      final existingRequest = await _friendRequestsCollection
          .where('senderId', isEqualTo: currentUser)
          .where('receiverId', isEqualTo: targetUser.uId)
          .where('status', isEqualTo: 'pending')
          .get();

      if (existingRequest.docs.isNotEmpty) {
        if (kDebugMode) {
          print('⚠️ Friend request already exists');
        }
        return false;
      }

      // Create friend request
      final friendRequest = FriendRequestModel(
        id: '',
        senderId: currentUser,
        senderName: currentUserData.name ?? '',
        senderImage: currentUserData.image ?? '',
        receiverId: targetUser.uId ?? '',
        receiverName: targetUser.name ?? '',
        receiverImage: targetUser.image ?? '',
        status: FriendRequestStatus.pending,
        createdAt: DateTime.now(),
      );

      final docRef =
          await _friendRequestsCollection.add(friendRequest.toJson());

      if (kDebugMode) {
        print('✅ Friend request sent successfully: ${docRef.id}');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending friend request: $e');
      }
      return false;
    }
  }

  // Get pending friend requests (received by current user)
  static Future<List<FriendRequestModel>> getPendingFriendRequests() async {
    try {
      final currentUser = currentUserId;
      if (kDebugMode) {
        debugPrint('🔍 Getting pending friend requests for user: $currentUser');
      }

      if (currentUser == null) {
        if (kDebugMode) {
          debugPrint('❌ No current user found');
        }
        return [];
      }

      if (kDebugMode) {
        debugPrint('📡 Querying friendRequests collection...');
      }

      final snapshot = await _friendRequestsCollection
          .where('receiverId', isEqualTo: currentUser)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();

      if (kDebugMode) {
        debugPrint('📊 Found ${snapshot.docs.length} documents in query');
        for (var doc in snapshot.docs) {
          debugPrint('📄 Document: ${doc.id} - ${doc.data()}');
        }
      }

      final requests = snapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();

      if (kDebugMode) {
        debugPrint('✅ Returning ${requests.length} friend requests');
      }

      return requests;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error getting pending friend requests: $e');
      }
      return [];
    }
  }

  // Accept friend request
  static Future<bool> acceptFriendRequest(FriendRequestModel request) async {
    try {
      if (kDebugMode) {
        debugPrint('\n🎉 === ACCEPTING FRIEND REQUEST ===');
        debugPrint('📨 Request ID: ${request.id}');
        debugPrint('👤 From: ${request.senderName} (${request.senderId})');
        debugPrint('👤 To: ${request.receiverName} (${request.receiverId})');
      }

      final batch = _firestore.batch();

      // Update friend request status
      batch.update(
        _friendRequestsCollection.doc(request.id),
        {
          'status': 'accepted',
          'updatedAt': Timestamp.now(),
        },
      );

      // Create friendship
      final friendship = FriendshipModel(
        id: '',
        user1Id: request.senderId,
        user1Name: request.senderName,
        user1Image: request.senderImage,
        user2Id: request.receiverId,
        user2Name: request.receiverName,
        user2Image: request.receiverImage,
        createdAt: DateTime.now(),
      );

      batch.set(_friendshipsCollection.doc(), friendship.toJson());

      await batch.commit();

      if (kDebugMode) {
        debugPrint('✅ Friend request accepted successfully!');
        debugPrint(
            '🤝 New friendship created between ${request.senderName} and ${request.receiverName}');
        debugPrint('=====================================\n');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error accepting friend request: $e');
      }
      return false;
    }
  }

  // Reject friend request
  static Future<bool> rejectFriendRequest(FriendRequestModel request) async {
    try {
      await _friendRequestsCollection.doc(request.id).update({
        'status': 'rejected',
        'updatedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error rejecting friend request: $e');
      }
      return false;
    }
  }

  // Get friend requests count (for notification badge)
  static Future<int> getPendingFriendRequestsCount() async {
    try {
      final requests = await getPendingFriendRequests();
      return requests.length;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting friend requests count: $e');
      }
      return 0;
    }
  }

  // Stream to listen to friend requests in real-time
  static Stream<List<FriendRequestModel>> listenToPendingFriendRequests() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value([]);

    return _friendRequestsCollection
        .where('receiverId', isEqualTo: currentUser)
        .where('status', isEqualTo: 'pending')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      if (kDebugMode) {
        print(
            '📬 Received friend requests update: ${snapshot.docs.length} requests');
      }
      return snapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    });
  }

  // Stream to listen to all friend request changes (sent and received)
  static Stream<List<FriendRequestModel>> listenToAllFriendRequests() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value([]);

    return _friendRequestsCollection
        .where('status', isEqualTo: 'pending')
        .where(Filter.or(
          Filter('senderId', isEqualTo: currentUser),
          Filter('receiverId', isEqualTo: currentUser),
        ))
        .snapshots()
        .map((snapshot) {
      if (kDebugMode) {
        print(
            '🔄 All friend requests update: ${snapshot.docs.length} requests');
      }
      return snapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    });
  }

  // Debug method to create a test friend request
  static Future<bool> createTestFriendRequest() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) {
        if (kDebugMode) {
          debugPrint('❌ No current user for test');
        }
        return false;
      }

      // Create a test friend request document
      await _friendRequestsCollection.add({
        'senderId': 'test_user_123',
        'senderName': 'Test User',
        'senderImage': '',
        'receiverId': currentUser,
        'receiverName': 'Current User',
        'receiverImage': '',
        'status': 'pending',
        'createdAt': Timestamp.now(),
      });

      if (kDebugMode) {
        debugPrint('✅ Test friend request created');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error creating test friend request: $e');
      }
      return false;
    }
  }

  // Get friend request status between current user and target user
  static Future<FriendRequestStatus?> getFriendRequestStatus(
      String targetUserId) async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return null;

      // Check for any existing friend request (sent or received)
      final sentRequestSnapshot = await _friendRequestsCollection
          .where('senderId', isEqualTo: currentUser)
          .where('receiverId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      final receivedRequestSnapshot = await _friendRequestsCollection
          .where('senderId', isEqualTo: targetUserId)
          .where('receiverId', isEqualTo: currentUser)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      // Check sent request first
      if (sentRequestSnapshot.docs.isNotEmpty) {
        final request = FriendRequestModel.fromJson(
          sentRequestSnapshot.docs.first.data() as Map<String, dynamic>,
          sentRequestSnapshot.docs.first.id,
        );
        return request.status;
      }

      // Then check received request
      if (receivedRequestSnapshot.docs.isNotEmpty) {
        final request = FriendRequestModel.fromJson(
          receivedRequestSnapshot.docs.first.data() as Map<String, dynamic>,
          receivedRequestSnapshot.docs.first.id,
        );
        return request.status;
      }

      return null; // No request found
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting friend request status: $e');
      }
      return null;
    }
  }

  // Stream to listen to friends list in real-time
  static Stream<List<FriendshipModel>> listenToFriends() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('friendships')
        .where(Filter.or(
          Filter('user1Id', isEqualTo: currentUser),
          Filter('user2Id', isEqualTo: currentUser),
        ))
        .snapshots()
        .map((snapshot) {
      if (kDebugMode) {
        print('👥 Friends list update: ${snapshot.docs.length} friends');
      }
      return snapshot.docs
          .map((doc) => FriendshipModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    });
  }

  // Stream to listen to available users in real-time
  static Stream<List<UserModel>> listenToAvailableUsers() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value([]);

    // Create a stream controller to combine friend and user updates
    final controller = StreamController<List<UserModel>>();

    // Listen to friendships to get current friend list
    _friendshipsCollection
        .where(Filter.or(
          Filter('user1Id', isEqualTo: currentUser),
          Filter('user2Id', isEqualTo: currentUser),
        ))
        .snapshots()
        .listen((friendsSnapshot) async {
      try {
        // Get friend IDs
        final friendIds = friendsSnapshot.docs.map((doc) {
          final friendship = FriendshipModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id);
          return friendship.user1Id == currentUser
              ? friendship.user2Id
              : friendship.user1Id;
        }).toSet();

        // Get pending request IDs
        final pendingRequests = await getPendingFriendRequests();
        final pendingIds = pendingRequests
            .map((request) => request.senderId == currentUser
                ? request.receiverId
                : request.senderId)
            .toSet();

        // Get all users except current user, friends, and pending requests
        final QuerySnapshot<Map<String, dynamic>> usersSnapshot =
            await _usersCollection.where('uId', isNotEqualTo: currentUser).get()
                as QuerySnapshot<Map<String, dynamic>>;

        final availableUsers = usersSnapshot.docs
            .map((doc) {
              final data = doc.data();
              return UserModel.fromJson(data);
            })
            .where((user) =>
                user.uId != null &&
                user.uId!.isNotEmpty &&
                !friendIds.contains(user.uId) &&
                !pendingIds.contains(user.uId))
            .toList();

        if (kDebugMode) {
          print('👥 Available users update: ${availableUsers.length} users');
          print('🤝 Excluding ${friendIds.length} friends');
          print('⏳ Excluding ${pendingIds.length} pending requests');
        }

        controller.add(availableUsers);
      } catch (e) {
        if (kDebugMode) {
          print('Error updating available users: $e');
        }
        controller.add([]);
      }
    });

    return controller.stream;
  }

  // Combined stream for friends page real-time updates
  static Stream<Map<String, dynamic>> listenToFriendsPageUpdates() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value({});

    // Create a stream controller to combine updates
    final controller = StreamController<Map<String, dynamic>>();
    List<FriendshipModel>? latestFriends;
    List<FriendRequestModel>? latestRequests;
    List<UserModel>? latestUsers;

    // Listen to friends list
    _firestore
        .collection('friendships')
        .where(Filter.or(
          Filter('user1Id', isEqualTo: currentUser),
          Filter('user2Id', isEqualTo: currentUser),
        ))
        .snapshots()
        .listen((snapshot) {
      latestFriends = snapshot.docs
          .map((doc) => FriendshipModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
      _emitCombinedData(controller, latestFriends, latestRequests, latestUsers);
    });

    // Listen to friend requests
    _friendRequestsCollection
        .where(Filter.or(
          Filter('senderId', isEqualTo: currentUser),
          Filter('receiverId', isEqualTo: currentUser),
        ))
        .where('status', isEqualTo: 'pending')
        .snapshots()
        .listen((snapshot) {
      latestRequests = snapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
      _emitCombinedData(controller, latestFriends, latestRequests, latestUsers);
    });

    // Listen to all users
    _usersCollection.snapshots().listen((snapshot) {
      latestUsers = snapshot.docs
          .map((doc) => UserModel.fromJson(doc.data() as Map<String, dynamic>))
          .where((user) => user.uId != null && user.uId != currentUser)
          .toList();
      _emitCombinedData(controller, latestFriends, latestRequests, latestUsers);
    });

    // Return the stream and handle cleanup
    return controller.stream.asBroadcastStream()
      ..listen(null, onDone: () => controller.close());
  }

  static void _emitCombinedData(
    StreamController<Map<String, dynamic>> controller,
    List<FriendshipModel>? friends,
    List<FriendRequestModel>? requests,
    List<UserModel>? users,
  ) {
    // Only emit if we have all the data
    if (friends != null && requests != null && users != null) {
      if (kDebugMode) {
        print('🔄 Friends page update: ${friends.length} friends, '
            '${requests.length} requests, ${users.length} users');
      }
      controller.add({
        'friends': friends,
        'requests': requests,
        'users': users,
      });
    }
  }
}
