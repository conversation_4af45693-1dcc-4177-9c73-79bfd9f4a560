import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:movie_proj/feature/auth/model/user_model.dart';
import 'package:movie_proj/feature/myFriends/model/friend_request_model.dart';
import 'package:movie_proj/feature/myFriends/model/friendship_model.dart';

class FriendsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  static String? get currentUserId => _auth.currentUser?.uid;

  // Collections
  static CollectionReference get _usersCollection =>
      _firestore.collection('users');
  static CollectionReference get _friendRequestsCollection =>
      _firestore.collection('friendRequests');
  static CollectionReference get _friendshipsCollection =>
      _firestore.collection('friendships');

  // Get all users except current user and existing friends
  static Future<List<UserModel>> getAvailableUsers() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return [];

      // Get all users
      final usersSnapshot = await _usersCollection.get();
      final allUsers = usersSnapshot.docs
          .map((doc) => UserModel.fromJson(doc.data() as Map<String, dynamic>))
          .where((user) => user.uId != currentUser)
          .toList();

      // Get existing friends
      final friends = await getFriends();
      final friendIds = friends
          .map((friend) => friend.getFriendInfo(currentUser)['id'])
          .toSet();

      // Get pending friend requests (both sent and received)
      final pendingRequests = await getPendingFriendRequests();
      final pendingIds = pendingRequests
          .map((request) => request.senderId == currentUser
              ? request.receiverId
              : request.senderId)
          .toSet();

      // Filter out current user, existing friends, and users with pending requests
      final availableUsers = allUsers
          .where((user) =>
              !friendIds.contains(user.uId) && !pendingIds.contains(user.uId))
          .toList();

      return availableUsers;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting available users: $e');
      }
      return [];
    }
  }

  // Get user's friends
  static Future<List<FriendshipModel>> getFriends() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return [];

      final friendshipsSnapshot = await _friendshipsCollection
          .where('user1Id', isEqualTo: currentUser)
          .get();

      final friendshipsSnapshot2 = await _friendshipsCollection
          .where('user2Id', isEqualTo: currentUser)
          .get();

      final allFriendships = [
        ...friendshipsSnapshot.docs,
        ...friendshipsSnapshot2.docs,
      ];

      return allFriendships
          .map((doc) => FriendshipModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting friends: $e');
      }
      return [];
    }
  }

  // Send friend request
  static Future<bool> sendFriendRequest(UserModel targetUser) async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) return false;

      // Get current user data
      final currentUserDoc = await _usersCollection.doc(currentUser).get();
      if (!currentUserDoc.exists) return false;

      final currentUserData =
          UserModel.fromJson(currentUserDoc.data() as Map<String, dynamic>);

      // Create friend request
      final friendRequest = FriendRequestModel(
        id: '',
        senderId: currentUser,
        senderName: currentUserData.name ?? '',
        senderImage: currentUserData.image ?? '',
        receiverId: targetUser.uId ?? '',
        receiverName: targetUser.name ?? '',
        receiverImage: targetUser.image ?? '',
        status: FriendRequestStatus.pending,
        createdAt: DateTime.now(),
      );

      await _friendRequestsCollection.add(friendRequest.toJson());
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error sending friend request: $e');
      }
      return false;
    }
  }

  // Get pending friend requests (received by current user)
  static Future<List<FriendRequestModel>> getPendingFriendRequests() async {
    try {
      final currentUser = currentUserId;
      if (kDebugMode) {
        debugPrint('🔍 Getting pending friend requests for user: $currentUser');
      }

      if (currentUser == null) {
        if (kDebugMode) {
          debugPrint('❌ No current user found');
        }
        return [];
      }

      if (kDebugMode) {
        debugPrint('📡 Querying friendRequests collection...');
      }

      final snapshot = await _friendRequestsCollection
          .where('receiverId', isEqualTo: currentUser)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();

      if (kDebugMode) {
        debugPrint('📊 Found ${snapshot.docs.length} documents in query');
        for (var doc in snapshot.docs) {
          debugPrint('📄 Document: ${doc.id} - ${doc.data()}');
        }
      }

      final requests = snapshot.docs
          .map((doc) => FriendRequestModel.fromJson(
              doc.data() as Map<String, dynamic>, doc.id))
          .toList();

      if (kDebugMode) {
        debugPrint('✅ Returning ${requests.length} friend requests');
      }

      return requests;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error getting pending friend requests: $e');
      }
      return [];
    }
  }

  // Accept friend request
  static Future<bool> acceptFriendRequest(FriendRequestModel request) async {
    try {
      final batch = _firestore.batch();

      // Update friend request status
      batch.update(
        _friendRequestsCollection.doc(request.id),
        {
          'status': 'accepted',
          'updatedAt': Timestamp.now(),
        },
      );

      // Create friendship
      final friendship = FriendshipModel(
        id: '',
        user1Id: request.senderId,
        user1Name: request.senderName,
        user1Image: request.senderImage,
        user2Id: request.receiverId,
        user2Name: request.receiverName,
        user2Image: request.receiverImage,
        createdAt: DateTime.now(),
      );

      batch.set(_friendshipsCollection.doc(), friendship.toJson());

      await batch.commit();
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error accepting friend request: $e');
      }
      return false;
    }
  }

  // Reject friend request
  static Future<bool> rejectFriendRequest(FriendRequestModel request) async {
    try {
      await _friendRequestsCollection.doc(request.id).update({
        'status': 'rejected',
        'updatedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error rejecting friend request: $e');
      }
      return false;
    }
  }

  // Get friend requests count (for notification badge)
  static Future<int> getPendingFriendRequestsCount() async {
    try {
      final requests = await getPendingFriendRequests();
      return requests.length;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting friend requests count: $e');
      }
      return 0;
    }
  }

  // Listen to friend requests in real-time
  static Stream<List<FriendRequestModel>> listenToPendingFriendRequests() {
    final currentUser = currentUserId;
    if (currentUser == null) return Stream.value([]);

    return _friendRequestsCollection
        .where('receiverId', isEqualTo: currentUser)
        .where('status', isEqualTo: 'pending')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => FriendRequestModel.fromJson(
                doc.data() as Map<String, dynamic>, doc.id))
            .toList());
  }

  // Debug method to create a test friend request
  static Future<bool> createTestFriendRequest() async {
    try {
      final currentUser = currentUserId;
      if (currentUser == null) {
        if (kDebugMode) {
          debugPrint('❌ No current user for test');
        }
        return false;
      }

      // Create a test friend request document
      await _friendRequestsCollection.add({
        'senderId': 'test_user_123',
        'senderName': 'Test User',
        'senderImage': '',
        'receiverId': currentUser,
        'receiverName': 'Current User',
        'receiverImage': '',
        'status': 'pending',
        'createdAt': Timestamp.now(),
      });

      if (kDebugMode) {
        debugPrint('✅ Test friend request created');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error creating test friend request: $e');
      }
      return false;
    }
  }
}
